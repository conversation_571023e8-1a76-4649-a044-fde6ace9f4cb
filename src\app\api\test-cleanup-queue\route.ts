/**
 * Test Cleanup Queue API Route
 * 
 * This endpoint tests the cleanup queue functionality to diagnose
 * why database deletions are not triggering Cloudinary cleanup.
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    console.log('[Test Cleanup Queue] Checking cleanup queue status...')

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Test 1: Check if cleanup queue table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_name', 'cloudinary_cleanup_queue')

    console.log('[Test Cleanup Queue] Table check:', { tables, error: tablesError })

    // Test 2: Check cleanup queue contents
    const { data: queueItems, error: queueError } = await supabase
      .from('cloudinary_cleanup_queue')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)

    console.log('[Test Cleanup Queue] Queue items:', { count: queueItems?.length || 0, error: queueError })

    // Test 3: Check if triggers exist
    const { data: triggers, error: triggersError } = await supabase
      .from('information_schema.triggers')
      .select('trigger_name, event_object_table, action_timing, event_manipulation')
      .eq('event_object_table', 'media_assets')

    console.log('[Test Cleanup Queue] Triggers:', { triggers, error: triggersError })

    // Test 4: Check recent media_sync_log entries
    const { data: syncLogs, error: syncError } = await supabase
      .from('media_sync_log')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5)

    console.log('[Test Cleanup Queue] Recent sync logs:', { count: syncLogs?.length || 0, error: syncError })

    // Test 5: Try to call the cleanup queue function
    let functionResult = null
    let functionError = null
    try {
      const { data, error } = await supabase
        .rpc('process_lgu_cloudinary_cleanup_queue', { batch_size: 5 })
      functionResult = data
      functionError = error
    } catch (err) {
      functionError = err
    }

    console.log('[Test Cleanup Queue] Function test:', { result: functionResult, error: functionError })

    return NextResponse.json({
      success: true,
      test_results: {
        table_exists: !!tables && tables.length > 0,
        queue_items: {
          count: queueItems?.length || 0,
          items: queueItems || [],
          error: queueError?.message
        },
        triggers: {
          found: triggers || [],
          error: triggersError?.message
        },
        sync_logs: {
          count: syncLogs?.length || 0,
          recent: syncLogs || [],
          error: syncError?.message
        },
        function_test: {
          result: functionResult,
          error: functionError?.message
        }
      },
      recommendations: [
        !tables?.length && 'Run the updated entire-supabase-schema.sql to create cleanup queue table',
        !triggers?.length && 'Database triggers are missing - run the schema update',
        queueError && 'Cleanup queue table access error - check RLS policies',
        functionError && 'Cleanup function not available - run schema update'
      ].filter(Boolean),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Test Cleanup Queue] Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Cleanup queue test failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action = 'test_delete' } = await request.json()
    
    console.log(`[Test Cleanup Queue] Performing action: ${action}`)

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    if (action === 'test_delete') {
      // Create a test media asset and then delete it to trigger the cleanup queue
      const testAsset = {
        cloudinary_public_id: `test-delete-${Date.now()}`,
        cloudinary_version: 1,
        cloudinary_signature: 'test-signature',
        original_filename: 'test-file.jpg',
        file_size: 1024,
        mime_type: 'image/jpeg',
        format: 'jpg',
        secure_url: 'https://test.com/test.jpg',
        url: 'https://test.com/test.jpg',
        resource_type: 'image'
      }

      // Insert test asset
      const { data: insertedAsset, error: insertError } = await supabase
        .from('media_assets')
        .insert(testAsset)
        .select()
        .single()

      if (insertError) {
        throw new Error(`Failed to insert test asset: ${insertError.message}`)
      }

      console.log('[Test Cleanup Queue] Test asset created:', insertedAsset.id)

      // Delete the test asset to trigger cleanup queue
      const { error: deleteError } = await supabase
        .from('media_assets')
        .delete()
        .eq('id', insertedAsset.id)

      if (deleteError) {
        throw new Error(`Failed to delete test asset: ${deleteError.message}`)
      }

      console.log('[Test Cleanup Queue] Test asset deleted, checking queue...')

      // Check if cleanup queue entry was created
      const { data: queueEntry, error: queueError } = await supabase
        .from('cloudinary_cleanup_queue')
        .select('*')
        .eq('cloudinary_public_id', testAsset.cloudinary_public_id)
        .single()

      return NextResponse.json({
        success: true,
        message: 'Test deletion completed',
        results: {
          asset_created: !!insertedAsset,
          asset_deleted: !deleteError,
          queue_entry_created: !!queueEntry,
          queue_entry: queueEntry,
          queue_error: queueError?.message
        },
        timestamp: new Date().toISOString()
      })
    }

    return NextResponse.json({
      success: false,
      error: 'Unknown action',
      available_actions: ['test_delete']
    }, { status: 400 })

  } catch (error) {
    console.error('[Test Cleanup Queue] Action failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Cleanup queue action failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
