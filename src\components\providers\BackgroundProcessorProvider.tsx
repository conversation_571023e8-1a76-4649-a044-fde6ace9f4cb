'use client'

import { useEffect } from 'react'

/**
 * Background Processor Provider
 * 
 * Automatically starts the background cleanup processor when the app loads.
 * This ensures that database deletions trigger Cloudinary cleanup automatically.
 */
export function BackgroundProcessorProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Start the background processor when the app loads
    const startBackgroundProcessor = async () => {
      try {
        console.log('[BackgroundProcessorProvider] Starting background cleanup processor...')
        
        const response = await fetch('/api/background-processor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ action: 'start' })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('[BackgroundProcessorProvider] Background processor started:', result)
        } else {
          console.warn('[BackgroundProcessorProvider] Failed to start background processor:', response.status)
        }
      } catch (error) {
        console.error('[BackgroundProcessorProvider] Error starting background processor:', error)
      }
    }

    // Start after a short delay to ensure the app is fully loaded
    const timer = setTimeout(startBackgroundProcessor, 2000)

    return () => clearTimeout(timer)
  }, [])

  return <>{children}</>
}
