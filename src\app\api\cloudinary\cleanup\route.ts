/**
 * Cloudinary Cleanup API Route
 * 
 * Processes the cloudinary_cleanup_queue to ensure deleted database records
 * are also removed from Cloudinary. This endpoint can be called manually
 * or scheduled to run periodically.
 * 
 * Features:
 * - Process cleanup queue in batches
 * - Get cleanup statistics
 * - Retry failed cleanups
 * - Clean up old records
 * 
 * Usage:
 * POST /api/cloudinary/cleanup - Process cleanup queue
 * GET /api/cloudinary/cleanup - Get cleanup statistics
 * PUT /api/cloudinary/cleanup - Retry failed cleanups
 * DELETE /api/cloudinary/cleanup - Clean up old records
 */

import { NextRequest, NextResponse } from 'next/server'
import { CloudinaryCleanupService } from '@/lib/cloudinaryCleanupService'

/**
 * POST /api/cloudinary/cleanup
 * Process the cleanup queue
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[Cloudinary Cleanup API] Processing cleanup queue...')

    const body = await request.json().catch(() => ({}))
    const { batch_size = 10 } = body

    const result = await CloudinaryCleanupService.processCleanupQueue(batch_size)

    return NextResponse.json({
      success: result.success,
      message: `Processed ${result.processed} items: ${result.completed} completed, ${result.failed} failed`,
      data: {
        processed: result.processed,
        completed: result.completed,
        failed: result.failed,
        errors: result.errors
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Cloudinary Cleanup API] Processing failed:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Cleanup queue processing failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * GET /api/cloudinary/cleanup
 * Get cleanup queue statistics
 */
export async function GET() {
  try {
    console.log('[Cloudinary Cleanup API] Getting cleanup statistics...')

    const stats = await CloudinaryCleanupService.getCleanupStats()

    return NextResponse.json({
      success: true,
      message: 'Cleanup statistics retrieved successfully',
      data: {
        queue_stats: stats,
        summary: {
          total_items: stats.total_pending + stats.total_processing + stats.total_completed + stats.total_failed,
          active_items: stats.total_pending + stats.total_processing,
          needs_attention: stats.total_failed > 0 || stats.total_pending > 100
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Cloudinary Cleanup API] Statistics retrieval failed:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to retrieve cleanup statistics',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * PUT /api/cloudinary/cleanup
 * Retry failed cleanup items
 */
export async function PUT(request: NextRequest) {
  try {
    console.log('[Cloudinary Cleanup API] Retrying failed cleanups...')

    const body = await request.json().catch(() => ({}))
    const { max_retries = 3 } = body

    const retryCount = await CloudinaryCleanupService.retryFailedCleanups(max_retries)

    return NextResponse.json({
      success: true,
      message: `Retried ${retryCount} failed cleanup items`,
      data: {
        retried_count: retryCount,
        max_retries: max_retries
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Cloudinary Cleanup API] Retry failed:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to retry cleanup items',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * DELETE /api/cloudinary/cleanup
 * Clean up old completed records
 */
export async function DELETE(request: NextRequest) {
  try {
    console.log('[Cloudinary Cleanup API] Cleaning up old records...')

    const body = await request.json().catch(() => ({}))
    const { days_old = 30 } = body

    const deletedCount = await CloudinaryCleanupService.cleanupOldRecords(days_old)

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${deletedCount} old records`,
      data: {
        deleted_count: deletedCount,
        days_old: days_old
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Cloudinary Cleanup API] Old record cleanup failed:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to clean up old records',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
