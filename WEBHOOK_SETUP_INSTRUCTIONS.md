# Cloudinary Cleanup Webhook Setup Instructions

## The Real Problem & Solution

**The Issue:** PostgreSQL database triggers cannot make HTTP requests to external APIs like Cloudinary. This is why deleting from Supabase wasn't triggering Cloudinary cleanup.

**The Solution:** Use Supabase Database Webhooks to call your Next.js API when records are deleted.

## Setup Steps

### 1. Deploy Your Application First
Make sure your application is deployed and accessible via HTTPS (required for webhooks).

### 2. Configure Supabase Database Webhook

1. **Go to your Supabase Dashboard**
2. **Navigate to Database > Webhooks**
3. **Click "Create a new hook"**
4. **Configure the webhook:**
   - **Name:** `cloudinary-cleanup-webhook`
   - **Table:** `media_assets`
   - **Events:** Check only `DELETE`
   - **Type:** `HTTP Request`
   - **Method:** `POST`
   - **URL:** `https://your-domain.com/api/webhooks/cloudinary-cleanup`
   - **HTTP Headers:** 
     ```
     Content-Type: application/json
     ```
   - **Conditions:** Leave empty (process all deletions)

### 3. Test the Webhook

1. **Test the endpoint directly:**
   ```bash
   curl https://your-domain.com/api/webhooks/cloudinary-cleanup
   ```
   Should return: `{"success": true, "message": "Cloudinary Cleanup Webhook endpoint is active"}`

2. **Test by deleting a record from Supabase:**
   ```sql
   DELETE FROM media_assets WHERE cloudinary_public_id = 'some-test-file';
   ```

3. **Check the webhook logs in Supabase Dashboard**

### 4. Alternative: Manual Processing (If Webhooks Don't Work)

If you can't use webhooks, you can process the cleanup queue manually:

1. **Run the cleanup processor:**
   ```bash
   curl -X POST https://your-domain.com/api/cloudinary/cleanup
   ```

2. **Set up a cron job to run this periodically**

## How It Works Now

### When you delete from Supabase:

1. ✅ **Database record deleted** (immediate)
2. ✅ **Database trigger fires** → Creates cleanup queue entry
3. ✅ **Webhook triggered** → Calls your API endpoint
4. ✅ **API endpoint** → Deletes from Cloudinary
5. ✅ **Cleanup queue updated** → Marked as completed
6. ✅ **Sync log created** → Full audit trail

### When you delete from Admin Panel:

1. ✅ **API call** → Deletes from Cloudinary
2. ✅ **Database deletion** → Removes from Supabase
3. ✅ **UI updates** → Removes from media library

## Verification

After setup, you can verify everything is working:

1. **Check webhook status:**
   ```bash
   GET https://your-domain.com/api/webhooks/cloudinary-cleanup
   ```

2. **Check cleanup queue:**
   ```bash
   GET https://your-domain.com/api/cloudinary/cleanup
   ```

3. **Test deletion from Supabase SQL Editor:**
   ```sql
   -- This should now trigger Cloudinary cleanup
   DELETE FROM media_assets WHERE cloudinary_public_id = 'test-file-id';
   ```

## Troubleshooting

### Webhook Not Triggering
- Ensure your app is deployed and accessible via HTTPS
- Check webhook configuration in Supabase Dashboard
- Verify the webhook URL is correct

### Cloudinary Deletion Failing
- Check your Cloudinary credentials in environment variables
- Verify the public_id exists in Cloudinary
- Check the resource_type is correct

### Database Issues
- Ensure you've run the updated `entire-supabase-schema.sql`
- Check that triggers are installed
- Verify cleanup queue table exists

## Environment Variables Required

Make sure these are set in your deployment:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

## Success Indicators

When everything is working correctly:

- ✅ Deleting from Supabase also deletes from Cloudinary
- ✅ Deleting from Admin Panel works as before
- ✅ Cleanup queue shows completed entries
- ✅ Sync logs show successful operations
- ✅ No orphaned files in Cloudinary
