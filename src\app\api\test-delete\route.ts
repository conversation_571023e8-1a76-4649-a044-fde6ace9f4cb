import { NextRequest, NextResponse } from 'next/server'
import { SupabaseMediaService } from '@/lib/supabaseMediaService'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const { public_id } = await request.json()
    
    if (!public_id) {
      return NextResponse.json({ error: 'public_id is required' }, { status: 400 })
    }

    console.log(`[Test Delete] Testing deletion for: ${public_id}`)

    // Test 1: Check if asset exists in database
    const asset = await SupabaseMediaService.getMediaAssetByPublicId(public_id)
    console.log(`[Test Delete] Asset found:`, asset ? 'YES' : 'NO')
    
    if (asset) {
      console.log(`[Test Delete] Asset details:`, {
        id: asset.id,
        public_id: asset.cloudinary_public_id,
        resource_type: asset.resource_type,
        uploaded_by: asset.uploaded_by
      })
    }

    // Test 2: Check service role authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Test service role by checking auth
    const { data: authData, error: authError } = await supabase.auth.getUser()
    console.log(`[Test Delete] Service role auth:`, {
      user: authData?.user?.id || 'NO USER',
      role: authData?.user?.role || 'NO ROLE',
      error: authError?.message || 'NO ERROR'
    })

    // Test 3: Try to delete related records manually
    if (asset) {
      console.log(`[Test Delete] Testing related record deletion...`)
      
      // Check media_usage records
      const { data: usageRecords, error: usageError } = await supabase
        .from('media_usage')
        .select('*')
        .eq('media_asset_id', asset.id)
      
      console.log(`[Test Delete] Media usage records:`, usageRecords?.length || 0)
      if (usageError) console.log(`[Test Delete] Usage query error:`, usageError)

      // Check media_collection_items records
      const { data: collectionRecords, error: collectionError } = await supabase
        .from('media_collection_items')
        .select('*')
        .eq('media_asset_id', asset.id)
      
      console.log(`[Test Delete] Collection item records:`, collectionRecords?.length || 0)
      if (collectionError) console.log(`[Test Delete] Collection query error:`, collectionError)

      // Test deletion of related records
      if (usageRecords && usageRecords.length > 0) {
        const { error: deleteUsageError } = await supabase
          .from('media_usage')
          .delete()
          .eq('media_asset_id', asset.id)
        
        console.log(`[Test Delete] Delete usage records result:`, deleteUsageError ? `ERROR: ${deleteUsageError.message}` : 'SUCCESS')
      }

      if (collectionRecords && collectionRecords.length > 0) {
        const { error: deleteCollectionError } = await supabase
          .from('media_collection_items')
          .delete()
          .eq('media_asset_id', asset.id)
        
        console.log(`[Test Delete] Delete collection records result:`, deleteCollectionError ? `ERROR: ${deleteCollectionError.message}` : 'SUCCESS')
      }

      // Test 4: Try to delete main record
      const { error: deleteError } = await supabase
        .from('media_assets')
        .delete()
        .eq('cloudinary_public_id', public_id)

      console.log(`[Test Delete] Delete main record result:`, deleteError ? `ERROR: ${deleteError.message}` : 'SUCCESS')
      
      if (deleteError) {
        console.log(`[Test Delete] Delete error details:`, {
          message: deleteError.message,
          code: deleteError.code,
          details: deleteError.details,
          hint: deleteError.hint
        })
      }
    }

    // Test 5: Environment variables check
    const envCheck = {
      supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      service_key_length: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0
    }
    console.log(`[Test Delete] Environment check:`, envCheck)

    return NextResponse.json({
      success: true,
      message: 'Test completed - check server logs for details',
      asset_found: !!asset,
      env_check: envCheck
    })

  } catch (error) {
    console.error('[Test Delete] Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
