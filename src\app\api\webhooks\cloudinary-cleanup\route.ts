/**
 * Cloudinary Cleanup Webhook
 * 
 * This webhook is triggered by Supabase Database Webhooks when records
 * are deleted from the media_assets table. It handles the actual Cloudinary
 * deletion to ensure proper synchronization.
 * 
 * Setup in Supabase:
 * 1. Go to Database > Webhooks
 * 2. Create new webhook
 * 3. Table: media_assets
 * 4. Events: DELETE
 * 5. URL: https://your-domain.com/api/webhooks/cloudinary-cleanup
 */

import { NextRequest, NextResponse } from 'next/server'
import { deleteFromCloudinary } from '@/lib/cloudinary'
import { createClient } from '@supabase/supabase-js'

interface WebhookPayload {
  type: 'DELETE'
  table: string
  record: {
    id: string
    cloudinary_public_id: string
    resource_type: string
    original_filename?: string
    folder?: string
  }
  schema: string
  old_record: any
}

export async function POST(request: NextRequest) {
  try {
    console.log('[Cloudinary Cleanup Webhook] Received webhook request')

    // Parse the webhook payload
    const payload: WebhookPayload = await request.json()
    
    console.log('[Cloudinary Cleanup Webhook] Payload:', {
      type: payload.type,
      table: payload.table,
      public_id: payload.old_record?.cloudinary_public_id
    })

    // Validate webhook payload
    if (payload.type !== 'DELETE' || payload.table !== 'media_assets') {
      console.log('[Cloudinary Cleanup Webhook] Ignoring non-DELETE event or wrong table')
      return NextResponse.json({ success: true, message: 'Event ignored' })
    }

    const record = payload.old_record
    if (!record?.cloudinary_public_id) {
      console.log('[Cloudinary Cleanup Webhook] No cloudinary_public_id found')
      return NextResponse.json({ success: true, message: 'No Cloudinary ID to delete' })
    }

    const publicId = record.cloudinary_public_id
    const resourceType = record.resource_type || 'image'

    console.log(`[Cloudinary Cleanup Webhook] Processing deletion for: ${publicId}`)

    // Initialize Supabase client for logging
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    try {
      // Delete from Cloudinary
      const cloudinaryResult = await deleteFromCloudinary(publicId, resourceType as any)
      
      console.log(`[Cloudinary Cleanup Webhook] Cloudinary deletion result:`, cloudinaryResult)

      // Check if deletion was successful
      const isSuccess = cloudinaryResult.result === 'ok' || cloudinaryResult.result === 'not found'
      
      if (isSuccess) {
        console.log(`[Cloudinary Cleanup Webhook] ✅ Successfully deleted from Cloudinary: ${publicId}`)
        
        // Update cleanup queue if it exists
        try {
          await supabase
            .from('cloudinary_cleanup_queue')
            .update({
              status: 'completed',
              processed_at: new Date().toISOString(),
              cloudinary_response: cloudinaryResult
            })
            .eq('cloudinary_public_id', publicId)
            .eq('status', 'pending')
        } catch (queueError) {
          console.warn('[Cloudinary Cleanup Webhook] Failed to update cleanup queue:', queueError)
        }

        // Log successful sync operation
        try {
          await supabase
            .from('media_sync_log')
            .insert({
              operation: 'delete',
              status: 'synced',
              cloudinary_public_id: publicId,
              source: 'webhook',
              operation_data: {
                webhook_triggered: true,
                cloudinary_result: cloudinaryResult,
                processed_at: new Date().toISOString()
              }
            })
        } catch (logError) {
          console.warn('[Cloudinary Cleanup Webhook] Failed to log sync operation:', logError)
        }

        return NextResponse.json({
          success: true,
          message: `Successfully deleted ${publicId} from Cloudinary`,
          cloudinary_result: cloudinaryResult
        })

      } else {
        console.error(`[Cloudinary Cleanup Webhook] ❌ Cloudinary deletion failed: ${cloudinaryResult.result}`)
        
        // Update cleanup queue as failed
        try {
          await supabase
            .from('cloudinary_cleanup_queue')
            .update({
              status: 'failed',
              processed_at: new Date().toISOString(),
              error_message: `Cloudinary deletion failed: ${cloudinaryResult.result}`,
              cloudinary_response: cloudinaryResult
            })
            .eq('cloudinary_public_id', publicId)
            .eq('status', 'pending')
        } catch (queueError) {
          console.warn('[Cloudinary Cleanup Webhook] Failed to update cleanup queue:', queueError)
        }

        return NextResponse.json({
          success: false,
          error: `Cloudinary deletion failed: ${cloudinaryResult.result}`,
          cloudinary_result: cloudinaryResult
        }, { status: 500 })
      }

    } catch (cloudinaryError) {
      console.error(`[Cloudinary Cleanup Webhook] ❌ Cloudinary deletion error:`, cloudinaryError)
      
      // Update cleanup queue as failed
      try {
        await supabase
          .from('cloudinary_cleanup_queue')
          .update({
            status: 'failed',
            processed_at: new Date().toISOString(),
            error_message: cloudinaryError instanceof Error ? cloudinaryError.message : 'Unknown error'
          })
          .eq('cloudinary_public_id', publicId)
          .eq('status', 'pending')
      } catch (queueError) {
        console.warn('[Cloudinary Cleanup Webhook] Failed to update cleanup queue:', queueError)
      }

      return NextResponse.json({
        success: false,
        error: cloudinaryError instanceof Error ? cloudinaryError.message : 'Unknown error',
        public_id: publicId
      }, { status: 500 })
    }

  } catch (error) {
    console.error('[Cloudinary Cleanup Webhook] Webhook processing failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Webhook processing failed'
    }, { status: 500 })
  }
}

// Handle GET requests for webhook verification
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Cloudinary Cleanup Webhook endpoint is active',
    timestamp: new Date().toISOString()
  })
}
