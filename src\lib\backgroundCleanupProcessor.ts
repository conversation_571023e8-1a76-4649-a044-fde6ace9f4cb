/**
 * Background Cleanup Processor
 * 
 * Automatically processes the Cloudinary cleanup queue in the background
 * when the application starts. This ensures that database deletions
 * automatically trigger Cloudinary cleanup without manual intervention.
 */

import { CloudinaryCleanupService } from './cloudinaryCleanupService'

class BackgroundCleanupProcessor {
  private static instance: BackgroundCleanupProcessor | null = null
  private intervalId: NodeJS.Timeout | null = null
  private isProcessing = false
  private readonly PROCESS_INTERVAL = 30000 // 30 seconds
  private readonly MAX_BATCH_SIZE = 10

  private constructor() {
    // Private constructor for singleton
  }

  static getInstance(): BackgroundCleanupProcessor {
    if (!BackgroundCleanupProcessor.instance) {
      BackgroundCleanupProcessor.instance = new BackgroundCleanupProcessor()
    }
    return BackgroundCleanupProcessor.instance
  }

  /**
   * Start the background processor
   */
  start(): void {
    if (this.intervalId) {
      console.log('[BackgroundCleanupProcessor] Already running')
      return
    }

    console.log('[BackgroundCleanupProcessor] Starting background cleanup processor...')
    
    // Process immediately on start
    this.processQueue()

    // Set up interval to process queue periodically
    this.intervalId = setInterval(() => {
      this.processQueue()
    }, this.PROCESS_INTERVAL)

    console.log(`[BackgroundCleanupProcessor] Background processor started (interval: ${this.PROCESS_INTERVAL}ms)`)
  }

  /**
   * Stop the background processor
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
      console.log('[BackgroundCleanupProcessor] Background processor stopped')
    }
  }

  /**
   * Process the cleanup queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      console.log('[BackgroundCleanupProcessor] Already processing, skipping...')
      return
    }

    this.isProcessing = true

    try {
      // Check if there are pending items first
      const stats = await CloudinaryCleanupService.getCleanupStats()
      
      if (stats.total_pending === 0) {
        // No pending items, nothing to do
        return
      }

      console.log(`[BackgroundCleanupProcessor] Found ${stats.total_pending} pending cleanup items`)

      // Process the queue
      const result = await CloudinaryCleanupService.processCleanupQueue(this.MAX_BATCH_SIZE)

      if (result.processed > 0) {
        console.log(`[BackgroundCleanupProcessor] Processed ${result.processed} items: ${result.completed} completed, ${result.failed} failed`)
        
        if (result.errors.length > 0) {
          console.warn('[BackgroundCleanupProcessor] Errors during processing:', result.errors)
        }
      }

    } catch (error) {
      console.error('[BackgroundCleanupProcessor] Error processing cleanup queue:', error)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Get processor status
   */
  getStatus(): {
    running: boolean
    processing: boolean
    interval: number
  } {
    return {
      running: this.intervalId !== null,
      processing: this.isProcessing,
      interval: this.PROCESS_INTERVAL
    }
  }

  /**
   * Process queue immediately (manual trigger)
   */
  async processNow(): Promise<void> {
    console.log('[BackgroundCleanupProcessor] Manual processing triggered')
    await this.processQueue()
  }
}

// Export singleton instance
export const backgroundCleanupProcessor = BackgroundCleanupProcessor.getInstance()

// Auto-start in production (server-side only)
if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
  // Start the processor when the module is imported
  setTimeout(() => {
    backgroundCleanupProcessor.start()
  }, 5000) // Wait 5 seconds after app start
}

export default BackgroundCleanupProcessor
