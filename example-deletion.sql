-- ========================================
-- LGU PROJECT MEDIA LIBRARY SYNC TRIGGERS
-- ========================================
-- This script creates database triggers that automatically
-- sync deletions with Cloudinary when records are deleted
-- directly from the media_assets table in your LGU Project
--
-- TAILORED FOR: LGU Project Media Library System
-- DATABASE SCHEMA: entire-supabase-schema.sql
-- INTEGRATION: Cloudinary + Supabase + Custom Media Library
-- ========================================

-- ========================================
-- STEP 1: CREATE CLOUDINARY CLEANUP FUNCTION
-- ========================================

-- Function to handle Cloudinary cleanup when media_assets are deleted
-- This integrates with your existing media_sync_log table for tracking
CREATE OR REPLACE FUNCTION cleanup_cloudinary_media_asset()
RETURNS TRIGGER AS $$
DECLARE
    cleanup_record_id UUID;
BEGIN
    -- Only process if the record has a cloudinary_public_id
    IF OLD.cloudinary_public_id IS NULL OR OLD.cloudinary_public_id = '' THEN
        RETURN OLD;
    END IF;

    -- Log the deletion trigger for debugging
    RAISE NOTICE 'LGU Media Library: Cloudinary cleanup triggered for public_id: %', OLD.cloudinary_public_id;

    -- Insert into cloudinary_cleanup_queue for the application to process
    -- This is safer than making direct HTTP calls from the database
    INSERT INTO cloudinary_cleanup_queue (
        cloudinary_public_id,
        original_filename,
        resource_type,
        file_size,
        folder,
        media_asset_id,
        deleted_at,
        trigger_source,
        sync_log_reference
    ) VALUES (
        OLD.cloudinary_public_id,
        OLD.original_filename,
        OLD.resource_type,
        OLD.file_size,
        OLD.folder,
        OLD.id,
        NOW(),
        'database_trigger',
        NULL -- Will be updated when sync log is created
    ) RETURNING id INTO cleanup_record_id;

    -- Create a sync log entry to track this deletion operation
    INSERT INTO media_sync_log (
        operation,
        status,
        media_asset_id,
        cloudinary_public_id,
        source,
        triggered_by,
        operation_data,
        created_at
    ) VALUES (
        'delete',
        'pending',
        OLD.id,
        OLD.cloudinary_public_id,
        'database_trigger',
        OLD.deleted_by,
        jsonb_build_object(
            'trigger_type', 'database_delete',
            'cleanup_queue_id', cleanup_record_id,
            'original_asset', row_to_json(OLD)
        ),
        NOW()
    );

    RETURN OLD;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the deletion
        RAISE WARNING 'LGU Media Library: Cloudinary cleanup failed for %: %', OLD.cloudinary_public_id, SQLERRM;
        RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- STEP 2: CREATE CLEANUP QUEUE TABLE
-- ========================================

-- Table to queue Cloudinary cleanup operations
-- Tailored for LGU Project with proper UUID support and integration
CREATE TABLE IF NOT EXISTS cloudinary_cleanup_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cloudinary_public_id VARCHAR(500) NOT NULL,
    original_filename VARCHAR(500),
    resource_type VARCHAR(50) DEFAULT 'image',
    file_size BIGINT,
    folder VARCHAR(500),
    media_asset_id UUID, -- Reference to the deleted media_assets record
    deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    trigger_source VARCHAR(100) DEFAULT 'unknown',
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, failed
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    sync_log_reference UUID, -- Reference to media_sync_log entry
    cloudinary_response JSONB, -- Store Cloudinary API response
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_resource_type CHECK (resource_type IN ('image', 'video', 'raw')),
    CONSTRAINT valid_trigger_source CHECK (trigger_source IN ('database_trigger', 'api_call', 'manual', 'batch_cleanup'))
);

-- Indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_cloudinary_cleanup_status ON cloudinary_cleanup_queue(status, created_at);
CREATE INDEX IF NOT EXISTS idx_cloudinary_cleanup_public_id ON cloudinary_cleanup_queue(cloudinary_public_id);
CREATE INDEX IF NOT EXISTS idx_cloudinary_cleanup_media_asset ON cloudinary_cleanup_queue(media_asset_id);
CREATE INDEX IF NOT EXISTS idx_cloudinary_cleanup_trigger_source ON cloudinary_cleanup_queue(trigger_source);

-- Add updated_at trigger
DROP TRIGGER IF EXISTS update_cloudinary_cleanup_queue_updated_at ON cloudinary_cleanup_queue;
CREATE TRIGGER update_cloudinary_cleanup_queue_updated_at
    BEFORE UPDATE ON cloudinary_cleanup_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- STEP 3: CREATE TRIGGERS FOR LGU MEDIA LIBRARY
-- ========================================

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS trigger_cloudinary_cleanup_on_delete ON media_assets;
DROP TRIGGER IF EXISTS trigger_cloudinary_cleanup_on_hard_delete ON media_assets;

-- Trigger for hard deletes on media_assets table
-- This will automatically queue Cloudinary cleanup when records are deleted
CREATE TRIGGER trigger_cloudinary_cleanup_on_delete
    AFTER DELETE ON media_assets
    FOR EACH ROW
    EXECUTE FUNCTION cleanup_cloudinary_media_asset();

-- Additional trigger for soft deletes (when deleted_at is set)
-- This helps track when items are marked for deletion
CREATE OR REPLACE FUNCTION handle_media_asset_soft_delete()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process when deleted_at changes from NULL to a timestamp
    IF OLD.deleted_at IS NULL AND NEW.deleted_at IS NOT NULL THEN
        RAISE NOTICE 'LGU Media Library: Soft delete detected for public_id: %', NEW.cloudinary_public_id;

        -- Log the soft delete operation
        INSERT INTO media_sync_log (
            operation,
            status,
            media_asset_id,
            cloudinary_public_id,
            source,
            triggered_by,
            operation_data,
            created_at
        ) VALUES (
            'delete',
            'pending',
            NEW.id,
            NEW.cloudinary_public_id,
            'soft_delete_trigger',
            NEW.deleted_by,
            jsonb_build_object(
                'trigger_type', 'soft_delete',
                'deleted_at', NEW.deleted_at,
                'soft_delete', true
            ),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for soft deletes
DROP TRIGGER IF EXISTS trigger_media_asset_soft_delete ON media_assets;
CREATE TRIGGER trigger_media_asset_soft_delete
    AFTER UPDATE ON media_assets
    FOR EACH ROW
    EXECUTE FUNCTION handle_media_asset_soft_delete();

-- ========================================
-- STEP 4: CREATE CLEANUP PROCESSING FUNCTIONS FOR LGU PROJECT
-- ========================================

-- Function to process the cleanup queue (to be called by your Node.js application)
CREATE OR REPLACE FUNCTION process_lgu_cloudinary_cleanup_queue(
    batch_size INTEGER DEFAULT 10
) RETURNS TABLE(
    queue_id UUID,
    cloudinary_public_id VARCHAR(500),
    resource_type VARCHAR(50),
    original_filename VARCHAR(500),
    folder VARCHAR(500),
    retry_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Mark items as processing and return them for the application to handle
    UPDATE cloudinary_cleanup_queue
    SET
        status = 'processing',
        processed_at = NOW(),
        updated_at = NOW()
    WHERE id IN (
        SELECT id FROM cloudinary_cleanup_queue
        WHERE status = 'pending'
        ORDER BY created_at ASC
        LIMIT batch_size
    );

    -- Return the items that need processing
    RETURN QUERY
    SELECT
        ccq.id,
        ccq.cloudinary_public_id,
        ccq.resource_type,
        ccq.original_filename,
        ccq.folder,
        ccq.retry_count,
        ccq.created_at
    FROM cloudinary_cleanup_queue ccq
    WHERE ccq.status = 'processing'
    ORDER BY ccq.created_at ASC
    LIMIT batch_size;
END;
$$ LANGUAGE plpgsql;

-- Function to get cleanup queue statistics
CREATE OR REPLACE FUNCTION get_lgu_cleanup_queue_stats()
RETURNS TABLE(
    total_pending INTEGER,
    total_processing INTEGER,
    total_completed INTEGER,
    total_failed INTEGER,
    oldest_pending TIMESTAMP WITH TIME ZONE,
    newest_pending TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) FILTER (WHERE status = 'pending')::INTEGER,
        COUNT(*) FILTER (WHERE status = 'processing')::INTEGER,
        COUNT(*) FILTER (WHERE status = 'completed')::INTEGER,
        COUNT(*) FILTER (WHERE status = 'failed')::INTEGER,
        MIN(created_at) FILTER (WHERE status = 'pending'),
        MAX(created_at) FILTER (WHERE status = 'pending')
    FROM cloudinary_cleanup_queue;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- STEP 5: HELPER FUNCTIONS FOR LGU PROJECT
-- ========================================

-- Function to mark cleanup as completed
CREATE OR REPLACE FUNCTION mark_lgu_cloudinary_cleanup_completed(
    cleanup_id UUID,
    cloudinary_response JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE cloudinary_cleanup_queue
    SET
        status = 'completed',
        processed_at = NOW(),
        updated_at = NOW(),
        cloudinary_response = COALESCE(cloudinary_response, '{"result": "ok"}'::jsonb)
    WHERE id = cleanup_id;

    -- Update the corresponding sync log entry
    UPDATE media_sync_log
    SET
        status = 'synced',
        completed_at = NOW(),
        operation_data = operation_data || jsonb_build_object('cleanup_completed', true, 'cloudinary_response', cloudinary_response)
    WHERE operation_data->>'cleanup_queue_id' = cleanup_id::text;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to mark cleanup as failed
CREATE OR REPLACE FUNCTION mark_lgu_cloudinary_cleanup_failed(
    cleanup_id UUID,
    error_msg TEXT DEFAULT NULL,
    cloudinary_response JSONB DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE cloudinary_cleanup_queue
    SET
        status = 'failed',
        processed_at = NOW(),
        updated_at = NOW(),
        error_message = error_msg,
        retry_count = retry_count + 1,
        cloudinary_response = cloudinary_response
    WHERE id = cleanup_id;

    -- Update the corresponding sync log entry
    UPDATE media_sync_log
    SET
        status = 'error',
        error_message = error_msg,
        operation_data = operation_data || jsonb_build_object('cleanup_failed', true, 'error', error_msg, 'cloudinary_response', cloudinary_response)
    WHERE operation_data->>'cleanup_queue_id' = cleanup_id::text;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to retry failed cleanups
CREATE OR REPLACE FUNCTION retry_failed_lgu_cloudinary_cleanups(
    max_retries INTEGER DEFAULT 3
) RETURNS INTEGER AS $$
DECLARE
    retry_count INTEGER;
BEGIN
    UPDATE cloudinary_cleanup_queue
    SET
        status = 'pending',
        processed_at = NULL,
        updated_at = NOW()
    WHERE status = 'failed' AND retry_count < max_retries;

    GET DIAGNOSTICS retry_count = ROW_COUNT;

    -- Also update corresponding sync log entries
    UPDATE media_sync_log
    SET status = 'pending'
    WHERE status = 'error'
    AND operation_data->>'cleanup_queue_id' IN (
        SELECT id::text FROM cloudinary_cleanup_queue
        WHERE status = 'pending' AND retry_count <= max_retries
    );

    RETURN retry_count;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- STEP 6: CLEANUP OLD RECORDS
-- ========================================

-- Function to clean up old completed records
CREATE OR REPLACE FUNCTION cleanup_old_lgu_cloudinary_queue_records(
    days_old INTEGER DEFAULT 30
) RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cloudinary_cleanup_queue
    WHERE status = 'completed'
    AND processed_at < NOW() - INTERVAL '1 day' * days_old;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- STEP 6: INTEGRATION WITH EXISTING LGU FUNCTIONS
-- ========================================

-- Enhance existing soft_delete_media_asset function to work with cleanup queue
CREATE OR REPLACE FUNCTION soft_delete_media_asset(asset_id TEXT, deleted_by_user TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
    asset_uuid UUID;
    deleted_by_uuid UUID;
    cleanup_record_id UUID;
BEGIN
    IF deleted_by_user IS NOT NULL AND deleted_by_user != '' THEN
        deleted_by_uuid := deleted_by_user::UUID;
    END IF;

    -- Update the media asset
    UPDATE media_assets
    SET
        deleted_at = NOW(),
        deleted_by = deleted_by_uuid,
        sync_status = 'pending'
    WHERE cloudinary_public_id = asset_id AND deleted_at IS NULL;

    SELECT id INTO asset_uuid FROM media_assets WHERE cloudinary_public_id = asset_id;

    -- Add to cleanup queue for Cloudinary deletion
    INSERT INTO cloudinary_cleanup_queue (
        cloudinary_public_id,
        original_filename,
        resource_type,
        file_size,
        folder,
        media_asset_id,
        trigger_source,
        status
    )
    SELECT
        cloudinary_public_id,
        original_filename,
        resource_type,
        file_size,
        folder,
        id,
        'soft_delete_function',
        'pending'
    FROM media_assets
    WHERE cloudinary_public_id = asset_id
    RETURNING id INTO cleanup_record_id;

    -- Log the operation
    INSERT INTO media_sync_log (
        operation, status, media_asset_id, cloudinary_public_id,
        source, triggered_by, operation_data
    )
    VALUES (
        'delete', 'pending', asset_uuid, asset_id,
        'admin', deleted_by_uuid,
        jsonb_build_object(
            'soft_delete', true,
            'deleted_at', NOW(),
            'cleanup_queue_id', cleanup_record_id
        )
    );

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- VERIFICATION AND SETUP COMPLETE FOR LGU PROJECT
-- ========================================

-- Verify all triggers were created
DO $$
BEGIN
    -- Check main deletion trigger
    IF EXISTS (
        SELECT 1 FROM information_schema.triggers
        WHERE trigger_name = 'trigger_cloudinary_cleanup_on_delete'
        AND event_object_table = 'media_assets'
    ) THEN
        RAISE NOTICE '✅ LGU Media Library: Hard delete trigger created successfully!';
    ELSE
        RAISE NOTICE '❌ LGU Media Library: Failed to create hard delete trigger!';
    END IF;

    -- Check soft delete trigger
    IF EXISTS (
        SELECT 1 FROM information_schema.triggers
        WHERE trigger_name = 'trigger_media_asset_soft_delete'
        AND event_object_table = 'media_assets'
    ) THEN
        RAISE NOTICE '✅ LGU Media Library: Soft delete trigger created successfully!';
    ELSE
        RAISE NOTICE '❌ LGU Media Library: Failed to create soft delete trigger!';
    END IF;

    -- Check cleanup queue table
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'cloudinary_cleanup_queue'
    ) THEN
        RAISE NOTICE '✅ LGU Media Library: Cleanup queue table created successfully!';
    ELSE
        RAISE NOTICE '❌ LGU Media Library: Failed to create cleanup queue table!';
    END IF;
END $$;

-- Show current queue status (will be empty initially)
SELECT
    'LGU Media Library Cleanup Queue Status' as info,
    status,
    COUNT(*) as count,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM cloudinary_cleanup_queue
GROUP BY status
ORDER BY status;

-- Show a summary of the setup
SELECT
    'LGU Media Library Sync Setup Complete' as status,
    'Database triggers installed for automatic Cloudinary cleanup' as description,
    'Use process_lgu_cloudinary_cleanup_queue() to process pending deletions' as next_step;