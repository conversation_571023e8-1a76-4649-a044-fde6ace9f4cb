-- Fix DELETE policies for media_assets table
-- This script adds the missing DELETE policies that are preventing hard deletion

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow users to delete their own media assets" ON media_assets;
DROP POLICY IF EXISTS "Allow service role to delete media assets" ON media_assets;

-- Add DELETE policy for users to delete their own media assets
CREATE POLICY "Allow users to delete their own media assets" ON media_assets
    FOR DELETE USING (
        auth.role() = 'authenticated' AND 
        uploaded_by = auth.uid()
    );

-- Add DELETE policy for service role (bypasses all restrictions)
CREATE POLICY "Allow service role to delete media assets" ON media_assets
    FOR DELETE USING (auth.role() = 'service_role');

-- Also ensure related tables have proper DELETE policies

-- Media usage table
DROP POLICY IF EXISTS "Allow users to delete their media usage" ON media_usage;
DROP POLICY IF EXISTS "Allow service role to delete media usage" ON media_usage;

CREATE POLICY "Allow users to delete their media usage" ON media_usage
    FOR DELETE USING (
        auth.role() = 'authenticated' AND 
        EXISTS (
            SELECT 1 FROM media_assets 
            WHERE media_assets.id = media_usage.media_asset_id 
            AND media_assets.uploaded_by = auth.uid()
        )
    );

CREATE POLICY "Allow service role to delete media usage" ON media_usage
    FOR DELETE USING (auth.role() = 'service_role');

-- Media collection items table
DROP POLICY IF EXISTS "Allow users to delete their media collection items" ON media_collection_items;
DROP POLICY IF EXISTS "Allow service role to delete media collection items" ON media_collection_items;

CREATE POLICY "Allow users to delete their media collection items" ON media_collection_items
    FOR DELETE USING (
        auth.role() = 'authenticated' AND 
        EXISTS (
            SELECT 1 FROM media_assets 
            WHERE media_assets.id = media_collection_items.media_asset_id 
            AND media_assets.uploaded_by = auth.uid()
        )
    );

CREATE POLICY "Allow service role to delete media collection items" ON media_collection_items
    FOR DELETE USING (auth.role() = 'service_role');

-- Media sync log table (should allow deletion for cleanup)
DROP POLICY IF EXISTS "Allow service role to delete sync log" ON media_sync_log;

CREATE POLICY "Allow service role to delete sync log" ON media_sync_log
    FOR DELETE USING (auth.role() = 'service_role');

-- Verify the policies are created
SELECT schemaname, tablename, policyname, cmd, roles, qual 
FROM pg_policies 
WHERE tablename IN ('media_assets', 'media_usage', 'media_collection_items', 'media_sync_log')
AND cmd = 'DELETE'
ORDER BY tablename, policyname;
