/**
 * Comprehensive Sync Diagnosis API
 * 
 * This endpoint performs a complete diagnosis of the media library
 * synchronization system to identify why Cloudinary deletion isn't working.
 */

import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  const diagnosis = {
    timestamp: new Date().toISOString(),
    environment: {},
    database: {},
    cloudinary: {},
    sync_system: {},
    recommendations: [] as string[]
  }

  try {
    // 1. Environment Variables Check
    diagnosis.environment = {
      supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabase_service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      cloudinary_cloud_name: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      cloudinary_api_key: !!process.env.CLOUDINARY_API_KEY,
      cloudinary_api_secret: !!process.env.CLOUDINARY_API_SECRET,
      values: {
        supabase_url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        api_key_length: process.env.CLOUDINARY_API_KEY?.length || 0,
        api_secret_length: process.env.CLOUDINARY_API_SECRET?.length || 0
      }
    }

    // Add recommendations for missing env vars
    if (!diagnosis.environment.supabase_url) diagnosis.recommendations.push('Set NEXT_PUBLIC_SUPABASE_URL')
    if (!diagnosis.environment.supabase_service_key) diagnosis.recommendations.push('Set SUPABASE_SERVICE_ROLE_KEY')
    if (!diagnosis.environment.cloudinary_cloud_name) diagnosis.recommendations.push('Set NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME')
    if (!diagnosis.environment.cloudinary_api_key) diagnosis.recommendations.push('Set CLOUDINARY_API_KEY')
    if (!diagnosis.environment.cloudinary_api_secret) diagnosis.recommendations.push('Set CLOUDINARY_API_SECRET')

    // 2. Database Connection and Schema Check
    if (diagnosis.environment.supabase_url && diagnosis.environment.supabase_service_key) {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      )

      try {
        // Check if cleanup queue table exists
        const { data: cleanupTable, error: cleanupError } = await supabase
          .from('cloudinary_cleanup_queue')
          .select('count(*)')
          .limit(1)

        diagnosis.database.cleanup_queue_table = !cleanupError
        if (cleanupError) diagnosis.recommendations.push('Run updated entire-supabase-schema.sql to create cleanup queue table')

        // Check if triggers exist
        const { data: triggers } = await supabase
          .from('information_schema.triggers')
          .select('trigger_name')
          .eq('event_object_table', 'media_assets')

        diagnosis.database.triggers = triggers?.map(t => t.trigger_name) || []
        if (!triggers?.some(t => t.trigger_name === 'trigger_cloudinary_cleanup_on_delete')) {
          diagnosis.recommendations.push('Database triggers missing - run schema update')
        }

        // Check recent cleanup queue entries
        const { data: queueEntries } = await supabase
          .from('cloudinary_cleanup_queue')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5)

        diagnosis.database.recent_queue_entries = queueEntries || []

        // Test cleanup function
        try {
          const { data: functionTest } = await supabase
            .rpc('process_lgu_cloudinary_cleanup_queue', { batch_size: 1 })
          diagnosis.database.cleanup_function_works = true
        } catch {
          diagnosis.database.cleanup_function_works = false
          diagnosis.recommendations.push('Cleanup function not available - run schema update')
        }

      } catch (dbError) {
        diagnosis.database.error = dbError instanceof Error ? dbError.message : 'Unknown error'
        diagnosis.recommendations.push('Database connection failed - check credentials')
      }
    }

    // 3. Cloudinary Connection Check
    if (diagnosis.environment.cloudinary_cloud_name && 
        diagnosis.environment.cloudinary_api_key && 
        diagnosis.environment.cloudinary_api_secret) {
      
      try {
        const cloudinaryModule = await import('cloudinary')
        const cloudinary = cloudinaryModule.v2
        
        cloudinary.config({
          cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
          api_key: process.env.CLOUDINARY_API_KEY,
          api_secret: process.env.CLOUDINARY_API_SECRET,
          secure: true
        })

        // Test API connection by getting account info
        try {
          const usage = await cloudinary.api.usage()
          diagnosis.cloudinary.connection = true
          diagnosis.cloudinary.account_info = {
            plan: usage.plan,
            credits: usage.credits,
            used_percent: usage.used_percent
          }
        } catch (apiError: any) {
          diagnosis.cloudinary.connection = false
          diagnosis.cloudinary.error = apiError.message
          if (apiError.http_code === 401) {
            diagnosis.recommendations.push('Cloudinary authentication failed - check API credentials')
          }
        }

      } catch (importError) {
        diagnosis.cloudinary.import_error = importError instanceof Error ? importError.message : 'Unknown error'
        diagnosis.recommendations.push('Cloudinary SDK import failed')
      }
    }

    // 4. Sync System Status
    diagnosis.sync_system = {
      schema_updated: diagnosis.database.cleanup_queue_table && diagnosis.database.triggers.length > 0,
      triggers_active: diagnosis.database.triggers.includes('trigger_cloudinary_cleanup_on_delete'),
      cleanup_function_available: diagnosis.database.cleanup_function_works,
      pending_cleanups: diagnosis.database.recent_queue_entries?.filter(e => e.status === 'pending').length || 0,
      failed_cleanups: diagnosis.database.recent_queue_entries?.filter(e => e.status === 'failed').length || 0
    }

    // 5. Overall Health Assessment
    const criticalIssues = diagnosis.recommendations.filter(r => 
      r.includes('credentials') || r.includes('authentication') || r.includes('schema')
    ).length

    diagnosis.sync_system.health = criticalIssues === 0 ? 'healthy' : criticalIssues < 3 ? 'warning' : 'critical'

    // 6. Next Steps
    if (diagnosis.sync_system.health === 'critical') {
      diagnosis.recommendations.unshift('CRITICAL: Fix environment variables and run schema update first')
    } else if (diagnosis.sync_system.pending_cleanups > 0) {
      diagnosis.recommendations.push(`Process ${diagnosis.sync_system.pending_cleanups} pending cleanups with POST /api/cloudinary/cleanup`)
    } else if (diagnosis.sync_system.health === 'healthy') {
      diagnosis.recommendations.push('System appears healthy - test deletion manually')
    }

    return NextResponse.json({
      success: true,
      diagnosis,
      summary: {
        health: diagnosis.sync_system.health,
        critical_issues: criticalIssues,
        ready_for_testing: diagnosis.sync_system.health !== 'critical'
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      partial_diagnosis: diagnosis
    }, { status: 500 })
  }
}
