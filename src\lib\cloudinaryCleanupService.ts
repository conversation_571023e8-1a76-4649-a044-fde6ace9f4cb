/**
 * Cloudinary Cleanup Service
 * 
 * Processes the cloudinary_cleanup_queue to ensure deleted database records
 * are also removed from Cloudinary. This service runs as part of the 
 * LGU Project Media Library synchronization system.
 * 
 * Features:
 * - Processes cleanup queue in batches
 * - Handles retry logic for failed deletions
 * - Integrates with existing sync logging
 * - Supports different resource types (image, video, raw)
 * 
 * <AUTHOR> Project Team
 * @version 1.0.0
 */

import { createClient } from '@supabase/supabase-js'
import { deleteFromCloudinary } from './cloudinary'

interface CleanupQueueItem {
  queue_id: string
  cloudinary_public_id: string
  resource_type: 'image' | 'video' | 'raw'
  original_filename: string | null
  folder: string | null
  retry_count: number
  created_at: string
}

interface CleanupResult {
  success: boolean
  processed: number
  completed: number
  failed: number
  errors: string[]
}

export class CloudinaryCleanupService {
  private static supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )

  private static readonly MAX_RETRIES = 3
  private static readonly BATCH_SIZE = 10

  /**
   * Process pending cleanup items from the queue
   */
  static async processCleanupQueue(batchSize: number = this.BATCH_SIZE): Promise<CleanupResult> {
    const result: CleanupResult = {
      success: true,
      processed: 0,
      completed: 0,
      failed: 0,
      errors: []
    }

    try {
      console.log('[CloudinaryCleanupService] Starting cleanup queue processing...')

      // Get pending items from the queue
      const { data: queueItems, error: queueError } = await this.supabase
        .rpc('process_lgu_cloudinary_cleanup_queue', { batch_size: batchSize })

      if (queueError) {
        throw new Error(`Failed to fetch cleanup queue: ${queueError.message}`)
      }

      if (!queueItems || queueItems.length === 0) {
        console.log('[CloudinaryCleanupService] No pending cleanup items found')
        return result
      }

      console.log(`[CloudinaryCleanupService] Processing ${queueItems.length} cleanup items`)
      result.processed = queueItems.length

      // Process each item
      for (const item of queueItems as CleanupQueueItem[]) {
        try {
          await this.processCleanupItem(item)
          result.completed++
          console.log(`[CloudinaryCleanupService] ✅ Completed cleanup for: ${item.cloudinary_public_id}`)
        } catch (error) {
          result.failed++
          const errorMsg = error instanceof Error ? error.message : 'Unknown error'
          result.errors.push(`${item.cloudinary_public_id}: ${errorMsg}`)
          console.error(`[CloudinaryCleanupService] ❌ Failed cleanup for: ${item.cloudinary_public_id}`, error)
          
          // Mark as failed in the queue
          await this.markCleanupFailed(item.queue_id, errorMsg)
        }
      }

      result.success = result.failed === 0
      console.log(`[CloudinaryCleanupService] Batch complete: ${result.completed} completed, ${result.failed} failed`)

      return result

    } catch (error) {
      console.error('[CloudinaryCleanupService] Cleanup queue processing failed:', error)
      result.success = false
      result.errors.push(error instanceof Error ? error.message : 'Unknown error')
      return result
    }
  }

  /**
   * Process a single cleanup item
   */
  private static async processCleanupItem(item: CleanupQueueItem): Promise<void> {
    console.log(`[CloudinaryCleanupService] Processing cleanup for: ${item.cloudinary_public_id}`)

    try {
      // Delete from Cloudinary
      const cloudinaryResult = await deleteFromCloudinary(
        item.cloudinary_public_id,
        item.resource_type
      )

      console.log(`[CloudinaryCleanupService] Cloudinary delete result:`, cloudinaryResult)

      // Check if deletion was successful
      const isSuccess = cloudinaryResult.result === 'ok' || cloudinaryResult.result === 'not found'
      
      if (isSuccess) {
        // Mark as completed
        await this.markCleanupCompleted(item.queue_id, cloudinaryResult)
      } else {
        throw new Error(`Cloudinary deletion failed: ${cloudinaryResult.result}`)
      }

    } catch (error) {
      console.error(`[CloudinaryCleanupService] Error processing ${item.cloudinary_public_id}:`, error)
      throw error
    }
  }

  /**
   * Mark cleanup item as completed
   */
  private static async markCleanupCompleted(queueId: string, cloudinaryResponse: any): Promise<void> {
    const { error } = await this.supabase
      .rpc('mark_lgu_cloudinary_cleanup_completed', {
        cleanup_id: queueId,
        cloudinary_response: cloudinaryResponse
      })

    if (error) {
      console.error('[CloudinaryCleanupService] Failed to mark cleanup as completed:', error)
      throw new Error(`Failed to mark cleanup as completed: ${error.message}`)
    }
  }

  /**
   * Mark cleanup item as failed
   */
  private static async markCleanupFailed(queueId: string, errorMessage: string, cloudinaryResponse?: any): Promise<void> {
    const { error } = await this.supabase
      .rpc('mark_lgu_cloudinary_cleanup_failed', {
        cleanup_id: queueId,
        error_msg: errorMessage,
        cloudinary_response: cloudinaryResponse || null
      })

    if (error) {
      console.error('[CloudinaryCleanupService] Failed to mark cleanup as failed:', error)
      // Don't throw here to avoid infinite loops
    }
  }

  /**
   * Get cleanup queue statistics
   */
  static async getCleanupStats(): Promise<{
    total_pending: number
    total_processing: number
    total_completed: number
    total_failed: number
    oldest_pending: string | null
    newest_pending: string | null
  }> {
    const { data, error } = await this.supabase
      .rpc('get_lgu_cleanup_queue_stats')

    if (error) {
      throw new Error(`Failed to get cleanup stats: ${error.message}`)
    }

    return data[0] || {
      total_pending: 0,
      total_processing: 0,
      total_completed: 0,
      total_failed: 0,
      oldest_pending: null,
      newest_pending: null
    }
  }

  /**
   * Retry failed cleanup items
   */
  static async retryFailedCleanups(maxRetries: number = this.MAX_RETRIES): Promise<number> {
    const { data, error } = await this.supabase
      .rpc('retry_failed_lgu_cloudinary_cleanups', { max_retries: maxRetries })

    if (error) {
      throw new Error(`Failed to retry failed cleanups: ${error.message}`)
    }

    const retryCount = data || 0
    console.log(`[CloudinaryCleanupService] Retried ${retryCount} failed cleanup items`)
    return retryCount
  }

  /**
   * Clean up old completed records
   */
  static async cleanupOldRecords(daysOld: number = 30): Promise<number> {
    const { data, error } = await this.supabase
      .rpc('cleanup_old_lgu_cloudinary_queue_records', { days_old: daysOld })

    if (error) {
      throw new Error(`Failed to cleanup old records: ${error.message}`)
    }

    const deletedCount = data || 0
    console.log(`[CloudinaryCleanupService] Cleaned up ${deletedCount} old records`)
    return deletedCount
  }
}
