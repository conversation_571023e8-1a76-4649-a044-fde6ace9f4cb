/**
 * Background Processor Control API
 * 
 * Controls the background cleanup processor that automatically
 * processes the Cloudinary cleanup queue.
 */

import { NextRequest, NextResponse } from 'next/server'
import { backgroundCleanupProcessor } from '@/lib/backgroundCleanupProcessor'
import { CloudinaryCleanupService } from '@/lib/cloudinaryCleanupService'

/**
 * GET /api/background-processor
 * Get processor status and queue statistics
 */
export async function GET() {
  try {
    const processorStatus = backgroundCleanupProcessor.getStatus()
    const queueStats = await CloudinaryCleanupService.getCleanupStats()

    return NextResponse.json({
      success: true,
      data: {
        processor: processorStatus,
        queue: queueStats,
        summary: {
          auto_processing: processorStatus.running,
          pending_items: queueStats.total_pending,
          needs_attention: queueStats.total_failed > 0 || queueStats.total_pending > 50
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * POST /api/background-processor
 * Control processor (start/stop/process)
 */
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    switch (action) {
      case 'start':
        backgroundCleanupProcessor.start()
        return NextResponse.json({
          success: true,
          message: 'Background processor started',
          status: backgroundCleanupProcessor.getStatus()
        })

      case 'stop':
        backgroundCleanupProcessor.stop()
        return NextResponse.json({
          success: true,
          message: 'Background processor stopped',
          status: backgroundCleanupProcessor.getStatus()
        })

      case 'process':
        await backgroundCleanupProcessor.processNow()
        const queueStats = await CloudinaryCleanupService.getCleanupStats()
        return NextResponse.json({
          success: true,
          message: 'Manual processing completed',
          queue_stats: queueStats
        })

      case 'status':
        const status = backgroundCleanupProcessor.getStatus()
        const stats = await CloudinaryCleanupService.getCleanupStats()
        return NextResponse.json({
          success: true,
          processor: status,
          queue: stats
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          available_actions: ['start', 'stop', 'process', 'status']
        }, { status: 400 })
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
