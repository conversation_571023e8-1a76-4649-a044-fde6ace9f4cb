/**
 * Test Cloudinary Delete API Route
 * 
 * This endpoint tests Cloudinary deletion functionality to diagnose
 * why files are not being deleted from Cloudinary.
 * 
 * Based on professional documentation research:
 * - https://cloudinary.com/documentation/node_integration
 * - https://cloudinary.com/documentation/deleting_assets_tutorial
 * - https://cloudinary.com/documentation/image_upload_api_reference#destroy
 */

import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { public_id, resource_type = 'image' } = await request.json()
    
    if (!public_id) {
      return NextResponse.json({ error: 'public_id is required' }, { status: 400 })
    }

    console.log(`[Test Cloudinary Delete] Testing deletion for: ${public_id}`)

    // Test 1: Environment Variables Check
    const envCheck = {
      cloud_name: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key: !!process.env.CLOUDINARY_API_KEY,
      api_secret: !!process.env.CLOUDINARY_API_SECRET,
      cloud_name_value: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key_length: process.env.CLOUDINARY_API_KEY?.length || 0,
      api_secret_length: process.env.CLOUDINARY_API_SECRET?.length || 0
    }
    console.log('[Test Cloudinary Delete] Environment check:', envCheck)

    // Test 2: Import and Initialize Cloudinary
    let cloudinary: any = null
    try {
      const cloudinaryModule = await import('cloudinary')
      cloudinary = cloudinaryModule.v2
      
      // Configure Cloudinary with explicit values
      cloudinary.config({
        cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        api_key: process.env.CLOUDINARY_API_KEY,
        api_secret: process.env.CLOUDINARY_API_SECRET,
        secure: true
      })
      
      console.log('[Test Cloudinary Delete] Cloudinary configured successfully')
    } catch (importError) {
      console.error('[Test Cloudinary Delete] Cloudinary import failed:', importError)
      return NextResponse.json({
        success: false,
        error: 'Failed to import Cloudinary',
        details: importError instanceof Error ? importError.message : 'Unknown error'
      }, { status: 500 })
    }

    // Test 3: Check Cloudinary Configuration
    try {
      const config = cloudinary.config()
      console.log('[Test Cloudinary Delete] Cloudinary config:', {
        cloud_name: config.cloud_name,
        api_key: config.api_key ? `${config.api_key.substring(0, 6)}...` : 'NOT SET',
        api_secret: config.api_secret ? 'SET' : 'NOT SET'
      })
    } catch (configError) {
      console.error('[Test Cloudinary Delete] Config check failed:', configError)
    }

    // Test 4: Try to get asset info first (to verify it exists)
    let assetExists = false
    try {
      const assetInfo = await cloudinary.api.resource(public_id, {
        resource_type: resource_type
      })
      assetExists = true
      console.log('[Test Cloudinary Delete] Asset found:', {
        public_id: assetInfo.public_id,
        resource_type: assetInfo.resource_type,
        format: assetInfo.format,
        bytes: assetInfo.bytes,
        created_at: assetInfo.created_at
      })
    } catch (assetError: any) {
      console.log('[Test Cloudinary Delete] Asset lookup result:', {
        error: assetError.message,
        http_code: assetError.http_code,
        exists: false
      })
      
      if (assetError.http_code === 404) {
        return NextResponse.json({
          success: false,
          error: 'Asset not found in Cloudinary',
          public_id: public_id,
          resource_type: resource_type,
          message: 'The file does not exist in Cloudinary, so it cannot be deleted'
        })
      }
    }

    // Test 5: Attempt deletion with detailed error handling
    let deleteResult: any = null
    let deleteError: any = null
    
    try {
      console.log(`[Test Cloudinary Delete] Attempting to delete ${public_id} as ${resource_type}`)
      
      deleteResult = await cloudinary.uploader.destroy(public_id, {
        resource_type: resource_type,
        invalidate: true // Clear CDN cache
      })
      
      console.log('[Test Cloudinary Delete] Delete result:', deleteResult)
      
    } catch (error: any) {
      deleteError = error
      console.error('[Test Cloudinary Delete] Delete failed:', {
        message: error.message,
        http_code: error.http_code,
        error_code: error.error?.code,
        error_message: error.error?.message
      })
    }

    // Test 6: Try alternative resource types if first attempt failed
    const alternativeResults: any[] = []
    if (!deleteResult || deleteResult.result !== 'ok') {
      console.log('[Test Cloudinary Delete] Trying alternative resource types...')
      
      for (const altType of ['image', 'video', 'raw']) {
        if (altType === resource_type) continue // Skip the one we already tried
        
        try {
          const altResult = await cloudinary.uploader.destroy(public_id, {
            resource_type: altType,
            invalidate: true
          })
          
          alternativeResults.push({
            resource_type: altType,
            result: altResult,
            success: altResult.result === 'ok'
          })
          
          console.log(`[Test Cloudinary Delete] Alternative ${altType} result:`, altResult)
          
          if (altResult.result === 'ok') {
            deleteResult = altResult
            break
          }
        } catch (altError: any) {
          alternativeResults.push({
            resource_type: altType,
            error: altError.message,
            success: false
          })
        }
      }
    }

    // Return comprehensive test results
    return NextResponse.json({
      success: deleteResult?.result === 'ok',
      test_results: {
        environment_check: envCheck,
        asset_exists: assetExists,
        delete_result: deleteResult,
        delete_error: deleteError ? {
          message: deleteError.message,
          http_code: deleteError.http_code,
          error_details: deleteError.error
        } : null,
        alternative_attempts: alternativeResults
      },
      recommendations: [
        !envCheck.cloud_name && 'Set NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME environment variable',
        !envCheck.api_key && 'Set CLOUDINARY_API_KEY environment variable',
        !envCheck.api_secret && 'Set CLOUDINARY_API_SECRET environment variable',
        !assetExists && 'Verify the public_id exists in Cloudinary',
        deleteError && 'Check Cloudinary API credentials and permissions'
      ].filter(Boolean),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Test Cloudinary Delete] Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Cloudinary delete test failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
