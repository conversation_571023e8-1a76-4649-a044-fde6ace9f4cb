/**
 * Test Full Sync API
 * 
 * Tests the complete synchronization flow:
 * 1. Create a test media asset
 * 2. Delete it from Supabase
 * 3. Verify cleanup queue entry is created
 * 4. Process the cleanup queue
 * 5. Verify Cloudinary deletion
 */

import { NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { CloudinaryCleanupService } from '@/lib/cloudinaryCleanupService'

export async function POST() {
  const testResults = {
    step1_create_asset: false,
    step2_delete_from_db: false,
    step3_queue_entry_created: false,
    step4_process_queue: false,
    step5_cloudinary_deleted: false,
    errors: [] as string[],
    details: {} as any
  }

  try {
    console.log('[Test Full Sync] Starting complete sync test...')

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Step 1: Create a test media asset
    const testAsset = {
      cloudinary_public_id: `test-sync-${Date.now()}`,
      cloudinary_version: 1,
      cloudinary_signature: 'test-signature',
      original_filename: 'test-sync-file.jpg',
      file_size: 1024,
      mime_type: 'image/jpeg',
      format: 'jpg',
      secure_url: 'https://test.com/test-sync.jpg',
      url: 'https://test.com/test-sync.jpg',
      resource_type: 'image'
    }

    const { data: insertedAsset, error: insertError } = await supabase
      .from('media_assets')
      .insert(testAsset)
      .select()
      .single()

    if (insertError) {
      testResults.errors.push(`Step 1 failed: ${insertError.message}`)
      throw new Error(`Failed to create test asset: ${insertError.message}`)
    }

    testResults.step1_create_asset = true
    testResults.details.created_asset_id = insertedAsset.id
    console.log('[Test Full Sync] ✅ Step 1: Test asset created')

    // Step 2: Delete from database (this should trigger the cleanup queue)
    const { error: deleteError } = await supabase
      .from('media_assets')
      .delete()
      .eq('id', insertedAsset.id)

    if (deleteError) {
      testResults.errors.push(`Step 2 failed: ${deleteError.message}`)
      throw new Error(`Failed to delete test asset: ${deleteError.message}`)
    }

    testResults.step2_delete_from_db = true
    console.log('[Test Full Sync] ✅ Step 2: Asset deleted from database')

    // Step 3: Check if cleanup queue entry was created
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second for trigger

    const { data: queueEntry, error: queueError } = await supabase
      .from('cloudinary_cleanup_queue')
      .select('*')
      .eq('cloudinary_public_id', testAsset.cloudinary_public_id)
      .single()

    if (queueError || !queueEntry) {
      testResults.errors.push(`Step 3 failed: Queue entry not created - ${queueError?.message || 'Not found'}`)
    } else {
      testResults.step3_queue_entry_created = true
      testResults.details.queue_entry = queueEntry
      console.log('[Test Full Sync] ✅ Step 3: Cleanup queue entry created')
    }

    // Step 4: Process the cleanup queue
    try {
      const processResult = await CloudinaryCleanupService.processCleanupQueue(1)
      testResults.step4_process_queue = processResult.processed > 0
      testResults.details.process_result = processResult
      
      if (processResult.processed > 0) {
        console.log('[Test Full Sync] ✅ Step 4: Cleanup queue processed')
      } else {
        testResults.errors.push('Step 4 failed: No items processed from queue')
      }
    } catch (processError) {
      testResults.errors.push(`Step 4 failed: ${processError instanceof Error ? processError.message : 'Unknown error'}`)
    }

    // Step 5: Verify cleanup queue entry is marked as completed
    const { data: updatedQueueEntry } = await supabase
      .from('cloudinary_cleanup_queue')
      .select('*')
      .eq('cloudinary_public_id', testAsset.cloudinary_public_id)
      .single()

    if (updatedQueueEntry?.status === 'completed') {
      testResults.step5_cloudinary_deleted = true
      testResults.details.final_queue_entry = updatedQueueEntry
      console.log('[Test Full Sync] ✅ Step 5: Cloudinary deletion completed')
    } else {
      testResults.errors.push(`Step 5 failed: Queue entry status is ${updatedQueueEntry?.status || 'unknown'}`)
      testResults.details.final_queue_entry = updatedQueueEntry
    }

    // Overall success check
    const allStepsSuccessful = testResults.step1_create_asset && 
                              testResults.step2_delete_from_db && 
                              testResults.step3_queue_entry_created && 
                              testResults.step4_process_queue && 
                              testResults.step5_cloudinary_deleted

    return NextResponse.json({
      success: allStepsSuccessful,
      message: allStepsSuccessful ? 
        'Complete sync test passed! Database deletion properly triggers Cloudinary cleanup.' :
        'Sync test failed. Check the details for specific issues.',
      test_results: testResults,
      summary: {
        total_steps: 5,
        passed_steps: [
          testResults.step1_create_asset,
          testResults.step2_delete_from_db,
          testResults.step3_queue_entry_created,
          testResults.step4_process_queue,
          testResults.step5_cloudinary_deleted
        ].filter(Boolean).length,
        all_passed: allStepsSuccessful
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Test Full Sync] Test failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      test_results: testResults,
      message: 'Full sync test failed with error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
